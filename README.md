# AI知识库系统 (AIKG)

一个基于Markdown的智能个人知识管理系统，旨在构建全方位的综合知识体系。系统采用模块化设计，每个根目录代表一个独立的知识领域，支持历史、数学、诗词、机械等多个学科的知识管理和学习。

## 🌟 核心特性

- **📝 Markdown支持**: 使用标准Markdown格式编写知识内容，支持YAML Front Matter元数据
- **🎨 卡片式展示**: 美观的卡片布局，支持分类和系列管理
- **🔍 智能搜索**: 支持标题、关键词、内容全文搜索
- **🔗 知识联系**: 卡片间可建立关联，形成知识网络
- **📱 响应式设计**: 适配桌面和移动设备
- **⚡ 动态加载**: 自动发现和加载新添加的内容
- **🎯 多领域管理**: 支持多个知识领域的独立管理
- **📚 深度内容**: 每个知识卡片都包含详实信息和现代启示
- **🏗️ 模块化架构**: 每个知识领域独立组织，便于扩展
- **🎓 教育价值**: 不仅记录知识，更提供现代应用和人生智慧

## 📊 项目统计

- **知识领域**: 6个独立知识库（历史、数学、诗词、机械、天文等）
- **总卡片数**: 100+ 张知识卡片
- **核心特色**: 中国历史完整体系（80+张卡片）
- **内容类型**: 人物传记、历史事件、学科知识、文学作品等
- **更新频率**: 持续更新中

## 🏗️ 知识库架构

### 📚 知识领域分布

**🏛️ chinese_history** - 中国历史专题库（80+张卡片）
- 完整的中国古代史体系，从夏朝到三国
- 包含朝代概述、重要人物、历史事件等

**📖 history** - 历史简述库（2张卡片）
- 中国古代史概述
- 欧洲古代史概述

**🔢 math** - 数学知识库（1张卡片）
- 代数基础知识
- 计划扩展：几何、微积分、统计等

**📝 poetry** - 诗词文学库（1张卡片）
- 刘禹锡诗人专题
- 计划扩展：唐诗宋词、文学史等

**⚙️ mechanical** - 机械工程库（规划中）
- 机械原理、工程设计等

**🌟 astronomy** - 天文学库（规划中）
- 天体物理、观测天文等

## 🌟 项目亮点

### 🏆 中国历史完整体系
本项目的核心成就是构建了**完整的中国古代史知识体系**：

**📊 规模统计**：
- **总计80+张卡片**，覆盖夏朝至三国时期
- **三国专题15张卡片**：人物传记8张、重要战役4张、政权介绍3张
- **战国专题12张卡片**：七雄完整体系
- **其他朝代50+张卡片**：夏商周、春秋、秦汉等

**🎯 内容特色**：
- **深度分析**：每张卡片3000-5000字，包含历史背景、人物分析、现代启示
- **系统关联**：通过connections建立知识网络，形成完整学习体系
- **现代价值**：不仅记录历史，更提供现代管理和人生智慧
- **严谨准确**：基于史料，确保内容的历史准确性

### 🔧 技术架构优势
- **模块化设计**：每个知识领域独立管理，便于扩展
- **统一格式**：所有卡片采用统一的Markdown+YAML格式
- **智能关联**：支持跨领域的知识关联
- **响应式界面**：适配各种设备的浏览体验

## 📁 项目结构

```
aikg/
├── index.html                 # 🌐 主页面（知识库浏览器）
├── config.json               # ⚙️ 系统配置文件
├── README.md                 # 📖 项目说明文档
├── chinese_history/          # 🏛️ 中国历史专题库（80+张卡片）
│   ├── 01-xia-dynasty/      # 夏朝（7张卡片）
│   ├── 02-shang-dynasty/    # 商朝（6张卡片）
│   ├── 03-western-zhou/     # 西周（8张卡片）
│   ├── 04-spring-autumn/    # 春秋（7张卡片）
│   ├── 05-warring-states/   # 战国（12张卡片）
│   ├── 06-qin-dynasty/      # 秦朝（5张卡片）
│   ├── 07-western-han/      # 西汉（10张卡片）
│   ├── 08-xin-dynasty/      # 新朝（1张卡片）
│   ├── 09-eastern-han/      # 东汉（11张卡片）
│   ├── 10-three-kingdoms/   # 三国（16张卡片）⭐ 完整体系
│   │   ├── overview.md      # 时期概述
│   │   ├── cao-cao.md       # 曹操（已有基础）
│   │   ├── liu-bei.md       # 刘备（已有基础）
│   │   ├── zhuge-liang.md   # 诸葛亮（已有基础）
│   │   ├── sun-quan.md      # 孙权 ✨
│   │   ├── guan-yu.md       # 关羽 ✨
│   │   ├── zhang-fei.md     # 张飞 ✨
│   │   ├── zhao-yun.md      # 赵云 ✨
│   │   ├── zhou-yu.md       # 周瑜 ✨
│   │   ├── sima-yi.md       # 司马懿 ✨
│   │   ├── cao-pi.md        # 曹丕 ✨
│   │   ├── lu-xun.md        # 陆逊 ✨
│   │   ├── battle-of-guandu.md # 官渡之战 ✨
│   │   ├── battle-of-chibi.md # 赤壁之战 ✨
│   │   ├── battle-of-yiling.md # 夷陵之战 ✨
│   │   ├── battle-of-jieting.md # 街亭之战 ✨
│   │   ├── wei-kingdom.md   # 魏国详细介绍 ✨
│   │   ├── shu-kingdom.md   # 蜀汉详细介绍 ✨
│   │   └── wu-kingdom.md    # 东吴详细介绍 ✨
│   └── timeline.md          # 历史时间线
├── history/                  # 📖 历史简述库（2张卡片）
│   ├── chinese-ancient.md   # 中国古代史概述
│   └── european-ancient.md  # 欧洲古代史概述
├── math/                     # 🔢 数学知识库（1张卡片）
│   └── algebra-basics.md    # 代数基础
├── poetry/                   # 📝 诗词文学库（1张卡片）
│   └── liuyuxi.md          # 刘禹锡诗人专题
├── mechanical/               # ⚙️ 机械工程库（规划中）
├── astronomy/                # 🌟 天文学库（规划中）
└── assets/                   # 静态资源
    ├── css/
    ├── js/
    └── images/
```

**架构说明**：
- ✨ 标记表示最新完善的内容（三国专题15张新卡片）
- 每个根目录代表一个独立的知识领域
- chinese_history是最完善的专题库，包含完整的古代史体系
- 其他领域（math、poetry等）为扩展方向，支持多学科知识管理

## 🚀 快速开始

### 1. 部署项目

将整个项目文件夹部署到Web服务器上，或使用本地服务器：

```bash
# 使用Python启动本地服务器
cd aikg
python -m http.server 8000

# 或使用Node.js
npx serve .

# 或使用PHP
php -S localhost:8000
```

### 2. 访问系统

在浏览器中打开 `http://localhost:8000` 即可访问知识库。

### 3. 浏览内容

AIKG包含多个知识领域，每个领域都有独立的知识体系：

**🏛️ 中国历史专题库（80+张卡片）：**
- **三国完整体系**：16张卡片，包含人物传记、重要战役、政权介绍
- **战国七雄体系**：12张卡片，七国详细介绍和重要人物
- **其他朝代**：夏商周、春秋、秦汉等50+张卡片

**📖 历史简述库（2张卡片）：**
- 中国古代史概述、欧洲古代史概述

**🔢 数学知识库（1张卡片）：**
- 代数基础知识（计划扩展：几何、微积分等）

**📝 诗词文学库（1张卡片）：**
- 刘禹锡诗人专题（计划扩展：唐诗宋词等）

**⚙️ 其他领域（规划中）：**
- 机械工程、天文学等专业领域

## 📚 内容特色

### 🏛️ 中国历史专题库 - 核心成就

#### 🌟 三国时期完整知识体系（16张卡片）
构建了最完整的三国时期知识体系：

**👑 重要人物（11位）**：
- **政治家**：曹操、刘备、孙权、曹丕、司马懿
- **军事家**：诸葛亮、周瑜、陆逊
- **武将**：关羽、张飞、赵云

**⚔️ 重要战役（4场）**：
- **官渡之战**：曹操统一北方的关键战役
- **赤壁之战**：决定三分天下格局的经典战役
- **夷陵之战**：改变三国力量对比的重要战役
- **街亭之战**：诸葛亮北伐的关键转折点

**🏛️ 三国政权（3个）**：
- **魏国**：最强大的政权，政治制度完善
- **蜀汉**：以兴复汉室为号召的政权
- **东吴**：存续时间最长，开发江南的政权

#### ⚔️ 战国七雄完整体系（12张卡片）
**🏰 七国详介**：秦、楚、齐、赵、韩、魏、燕
**📚 重要人物**：商鞅、孟子、荀子、韩非子等

#### 🏺 其他朝代体系（50+张卡片）
**夏商周**：各朝代概述和重要君主
**春秋秦汉**：重要历史人物和事件

### 🌐 多领域知识体系

#### 📖 历史简述库（2张卡片）
- 中国古代史概述、欧洲古代史概述

#### 🔢 数学知识库（1张卡片）
- 代数基础（计划扩展：几何、微积分、统计等）

#### 📝 诗词文学库（1张卡片）
- 刘禹锡诗人专题（计划扩展：唐诗宋词、文学史等）

#### ⚙️ 专业领域（规划中）
- 机械工程、天文学等专业知识领域

## 📝 知识卡片特色

### 🎨 统一的卡片结构

每个知识卡片都采用统一的结构设计，确保内容的系统性和可读性：

#### 元数据（YAML Front Matter）
```yaml
---
title: "卡片标题"
category: "history"
series: "中国历史"
period: "具体时间段"
keywords:
  - "关键词1"
  - "关键词2"
summary: "简短的概述描述"
difficulty: "初级/中级/高级"
estimated_time: "预计阅读时间"
author: "知识库管理员"
created_date: "2024-12-22"
updated_date: "2024-12-22"
connections:
  - id: "related-card-id"
    title: "相关卡片标题"
    type: "关系类型"
---
```

#### 内容结构（以人物卡片为例）
```markdown
# 人物姓名

## 基本信息
- 生卒年、字号、谥号等基本信息

## 生平经历
### 早年生活
### 重要事件
### 晚年经历

## 主要成就
### 政治成就
### 军事成就
### 文化成就

## 重要事件
### 事件1
### 事件2

## 性格特点
### 特点1
### 特点2

## 历史评价
### 正面评价
### 客观分析
### 历史影响

## 现代启示
### 领导智慧
### 品格修养
### 人生启示

## 扩展阅读
### 推荐书籍
### 相关资源
```

### 📊 内容质量标准

- **深度性**：每个卡片包含3000-5000字的详实内容
- **准确性**：基于史实，引用可靠史料
- **系统性**：内容结构完整，逻辑清晰
- **现代性**：提供现代启示和借鉴意义
- **关联性**：通过connections建立知识网络

## ⚙️ 技术实现

### 🔧 核心技术

- **前端框架**：原生HTML5 + CSS3 + JavaScript (ES6+)
- **Markdown解析**：marked.js
- **YAML解析**：js-yaml
- **图标库**：Font Awesome
- **样式设计**：响应式CSS，支持渐变和动画效果

### 📱 响应式设计

系统采用响应式设计，完美适配：
- **桌面端**：1200px以上宽屏显示
- **平板端**：768px-1199px中等屏幕
- **移动端**：767px以下小屏幕

### 🔍 搜索功能

智能搜索支持以下字段：
- **标题搜索**：卡片标题匹配
- **关键词搜索**：keywords字段匹配
- **内容搜索**：正文内容全文搜索
- **摘要搜索**：summary字段匹配

### 📂 文件管理

#### 添加新文件的步骤：

1. **创建Markdown文件**：
   - 在对应的朝代文件夹中创建新文件
   - 使用统一的YAML Front Matter格式
   - 按照标准的内容结构编写

2. **更新index.html**：
   ```javascript
   const knownFiles = [
       // 现有文件...
       'chinese_history/10-three-kingdoms/new-file.md',
   ];
   ```

3. **刷新页面**：
   - 系统会自动加载新文件
   - 新卡片会出现在对应的分类中

#### 文件命名规范：
- 使用英文小写字母
- 单词间用连字符(-)分隔
- 例如：`battle-of-chibi.md`、`zhuge-liang.md`

## 🔗 知识网络

### 卡片关联系统

每个知识卡片都可以与其他卡片建立关联，形成知识网络：

```yaml
connections:
  - id: "cao-cao"
    title: "曹操"
    type: "父亲"
  - id: "three-kingdoms"
    title: "三国时期"
    type: "历史时期"
  - id: "sima-yi"
    title: "司马懿"
    type: "重要大臣"
```

### 关联类型

- **历史时期**：所属的历史时期
- **父亲/母亲**：家庭关系
- **主公/部下**：政治关系
- **同僚/对手**：同时代关系
- **参战方/指挥者**：战役关系

### 知识图谱

通过关联系统，用户可以：
- 快速找到相关内容
- 理解历史人物和事件的关系
- 构建完整的知识体系
- 深度学习历史文化

## 🔧 技术特性

### 📝 Markdown 支持
- 标准 Markdown 语法
- 扩展元数据支持（YAML Front Matter）
- 自动链接生成
- 代码高亮支持
- 统一的卡片格式规范

### 🎨 界面设计
- 响应式卡片布局
- 深色/浅色主题切换
- 平滑动画效果
- 移动端优化
- 朝代分类展示

### 🔍 搜索功能
- 实时搜索
- 多字段搜索（标题、关键词、内容）
- 搜索结果高亮
- 搜索历史记录
- 分类筛选

### 🔗 关联系统
- 卡片间关联
- 自动关联发现
- 关联图谱展示
- 知识路径推荐
- 历史时期关联

### 📊 内容管理
- 自动内容发现
- 动态加载机制
- 内容统计分析
- 质量评估指标

## 🎯 学习路径推荐

### 🌟 综合知识库学习路径

#### 📚 历史爱好者路径
1. **历史简述** → 从history目录开始，了解历史概貌
2. **中国古代史** → 进入chinese_history，系统学习
3. **专题深入** → 选择感兴趣的朝代（如三国）深度学习
4. **跨领域拓展** → 结合诗词文学库，了解文化背景

#### 🔢 理科学习路径
1. **数学基础** → 从math目录的代数基础开始
2. **专业拓展** → 等待几何、微积分等内容更新
3. **工程应用** → 关注mechanical目录的工程知识
4. **科学探索** → 期待astronomy目录的天文知识

#### 🎨 文科综合路径
1. **文学欣赏** → 从poetry目录开始，学习古典诗词
2. **历史文化** → 结合chinese_history，了解文学背景
3. **思想哲学** → 学习历史人物的思想理念
4. **现代应用** → 思考古典文化的现代价值

#### 🌐 个人知识管理路径
1. **选择兴趣领域** → 从6个知识库中选择感兴趣的方向
2. **系统性学习** → 按照每个库的内部结构系统学习
3. **建立关联** → 利用卡片间的connections建立知识网络
4. **持续更新** → 关注项目更新，不断扩展知识面

### 🏛️ 中国历史专题推荐路径

#### 📚 三国入门路径
1. **时期概述** → `10-three-kingdoms/overview.md`
2. **三大政权** → 魏国、蜀汉、东吴介绍
3. **核心人物** → 曹操、刘备、孙权
4. **重要战役** → 官渡、赤壁、夷陵、街亭

#### ⚔️ 战国深度学习
1. **时代背景** → `05-warring-states/overview.md`
2. **七雄争霸** → 秦、楚、齐、赵、韩、魏、燕
3. **政治制度** → 各国政治制度比较
4. **文化成就** → 百家争鸣的思想文化

## 🚀 未来规划

### 📅 短期目标（1-3个月）
**🏛️ 中国历史库完善**：
- [x] ✅ 完善三国时期知识体系（已完成15张卡片）
- [x] ✅ 完善战国七雄知识体系（已完成12张卡片）
- [ ] 完善秦汉时期知识体系

**🌐 多领域扩展**：
- [ ] 扩展数学知识库（几何、微积分基础）
- [ ] 丰富诗词文学库（唐诗宋词专题）
- [ ] 启动机械工程库建设

**⚙️ 系统优化**：
- [ ] 优化搜索功能和用户体验
- [ ] 增加知识卡片的互动功能

### 🎯 中期目标（3-6个月）
**📚 内容建设**：
- [ ] 扩展到魏晋南北朝时期
- [ ] 完善隋唐五代知识体系
- [ ] 建设天文学知识库
- [ ] 添加更多数学专题

**🔧 功能增强**：
- [ ] 添加历史地图和时间线功能
- [ ] 开发移动端APP
- [ ] 增加用户学习进度追踪
- [ ] 跨领域知识关联功能

### 🌟 长期目标（6个月以上）
**🎯 内容目标**：
- [ ] 覆盖完整的中国古代史
- [ ] 建成完整的基础数学体系
- [ ] 扩展到物理、化学等理科领域
- [ ] 添加现代科技知识库

**💡 技术目标**：
- [ ] 添加多媒体内容（图片、视频）
- [ ] 开发AI问答功能
- [ ] 建立用户社区和讨论功能
- [ ] 智能推荐学习路径

## 📊 详细统计

### 📈 当前内容规模
**🌐 知识库分布**：
- **知识领域总数**：6个独立知识库
- **知识卡片总数**：100+ 张
- **总字数**：约 30万字

**📚 各领域统计**：
- **chinese_history**：80+ 张卡片（核心库）
- **history**：2 张卡片（概述库）
- **math**：1 张卡片（基础库）
- **poetry**：1 张卡片（文学库）
- **mechanical**：规划中
- **astronomy**：规划中

**🏛️ 中国历史库详细**：
- **三国时期**：16 张卡片（完整体系）
- **战国时期**：12 张卡片（七雄完整）
- **其他朝代**：50+ 张卡片

### 🏆 质量指标
- **平均卡片长度**：3000-5000字
- **内容深度**：包含历史背景、人物分析、现代启示
- **关联密度**：每个卡片平均关联3-5个其他卡片
- **更新频率**：持续更新和完善
- **特色亮点**：三国时期形成完整知识体系
- **技术特色**：模块化架构，支持多领域扩展

## 🤝 贡献指南

### 📝 内容贡献
欢迎贡献高质量的历史知识内容：

1. **Fork项目**并创建新分支
2. **按照模板**创建新的知识卡片
3. **确保内容准确性**，引用可靠史料
4. **提交Pull Request**，详细说明贡献内容

### 🐛 问题反馈
发现问题请通过以下方式反馈：
- 提交GitHub Issue
- 详细描述问题和复现步骤
- 提供浏览器和设备信息

### 💡 功能建议
欢迎提出新功能建议：
- 通过Issue描述功能需求
- 说明功能的使用场景和价值
- 提供具体的实现思路（可选）

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [marked.js](https://marked.js.org/) - Markdown解析库
- [js-yaml](https://github.com/nodeca/js-yaml) - YAML解析库
- [Font Awesome](https://fontawesome.com/) - 图标库
- 所有为中国历史文化传承做出贡献的学者和研究者

## 📞 联系方式

- **项目地址**：[GitHub Repository](https://github.com/your-username/aikg)
- **问题反馈**：通过GitHub Issues
- **功能建议**：通过GitHub Discussions

---

**AI知识库系统 (AIKG)** - 构建个人综合知识管理体系，让学习更加系统化、深度化、现代化。

*最后更新：2024年12月22日 - 综合知识库系统完善版* 🎓✨

**愿景**：打造最完整的个人知识管理系统，涵盖历史、数学、文学、工程等多个领域，为终身学习者提供强大的知识工具。
