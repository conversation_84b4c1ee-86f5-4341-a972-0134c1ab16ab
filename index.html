<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人知识库 - 动态版本</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Markdown解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- YAML解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/js-yaml@4.1.0/dist/js-yaml.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            margin: 50px 0;
        }

        .error {
            background: rgba(220, 53, 69, 0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        .category-nav {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 40px;
        }

        .category-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-weight: 500;
        }

        .category-btn:hover, .category-btn.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .knowledge-section {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .knowledge-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-title {
            color: white;
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .section-subtitle {
            color: rgba(255,255,255,0.9);
            text-align: center;
            margin-bottom: 40px;
            font-size: 1.1rem;
        }

        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }

        .card-series {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .card-series:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .series-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }

        .series-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .series-count {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 12px;
        }

        .knowledge-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .knowledge-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .knowledge-card:hover::before {
            transform: scaleX(1);
        }

        .knowledge-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
        }

        .card-title {
            font-size: 1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .card-period {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 8px;
            font-style: italic;
        }

        .card-keywords {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 10px;
        }

        .keyword-tag {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .card-summary {
            font-size: 0.85rem;
            color: #555;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .search-container {
            margin-bottom: 30px;
            text-align: center;
        }

        .search-box {
            width: 100%;
            max-width: 500px;
            padding: 15px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            background: rgba(255,255,255,1);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            backdrop-filter: blur(5px);
            z-index: 1000;
            animation: modalFadeIn 0.3s ease;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 40px;
            border-radius: 20px;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from { transform: translate(-50%, -60%); opacity: 0; }
            to { transform: translate(-50%, -50%); opacity: 1; }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }

        .modal-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #f0f0f0;
            color: #333;
        }

        .modal-body {
            line-height: 1.6;
            color: #333;
        }

        .modal-body h1, .modal-body h2, .modal-body h3 {
            color: #667eea;
            margin: 20px 0 10px;
        }

        .modal-body h4 {
            color: #2c3e50;
            margin: 15px 0 8px;
        }

        .modal-body ul, .modal-body ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        .modal-body li {
            margin-bottom: 5px;
        }

        .modal-body p {
            margin-bottom: 10px;
        }

        .modal-body strong {
            color: #2c3e50;
        }

        .connections {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .connections h4 {
            color: #667eea !important;
            margin-bottom: 10px;
        }

        .connection-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .connection-link {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            text-decoration: none;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .connection-link:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .cards-container {
                grid-template-columns: 1fr;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .modal-content {
                margin: 20px;
                padding: 25px;
                max-width: calc(100% - 40px);
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-book-open"></i> 个人知识库</h1>
            <p>探索历史脉络，串联知识体系</p>
        </header>

        <div class="search-container">
            <input type="text" class="search-box" placeholder="🔍 搜索知识卡片..." id="searchBox">
        </div>

        <nav class="category-nav" id="categoryNav">
            <!-- 分类导航将通过JavaScript动态生成 -->
        </nav>

        <div id="loadingMessage" class="loading">
            <i class="fas fa-spinner fa-spin"></i> 正在加载知识库...
        </div>

        <div id="errorMessage" class="error" style="display: none;">
            加载失败，请检查配置文件和网络连接。
        </div>

        <div id="knowledgeSections">
            <!-- 知识库内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal" id="cardModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">卡片标题</h2>
                <button class="close-btn" onclick="closeModal()">×</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 详细内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let config = null;
        let knowledgeCards = {};
        let currentCategory = 'chinese_history';

        // 初始化应用
        async function initApp() {
            try {
                await loadConfig();
                await loadAllCards();
                renderCategoryNav();
                renderKnowledgeSections();
                setupEventListeners();
                switchCategory(currentCategory);
                hideLoading();
            } catch (error) {
                console.error('初始化失败:', error);
                showError();
            }
        }

        // 加载配置文件
        async function loadConfig() {
            try {
                const response = await fetch('config.json');
                if (!response.ok) throw new Error('配置文件加载失败');
                config = await response.json();
                
                // 更新页面标题和描述
                document.title = config.site.title;
                document.querySelector('.header h1').innerHTML = 
                    `<i class="fas fa-book-open"></i> ${config.site.title}`;
                document.querySelector('.header p').textContent = config.site.subtitle;
            } catch (error) {
                throw new Error('配置文件加载失败: ' + error.message);
            }
        }

        // 加载所有卡片
        async function loadAllCards() {
            const loadPromises = [];
            
            for (const [categoryKey, category] of Object.entries(config.categories)) {
                for (const [seriesKey, series] of Object.entries(category.series)) {
                    // 尝试加载该系列的卡片
                    const promise = loadSeriesCards(categoryKey, seriesKey);
                    loadPromises.push(promise);
                }
            }
            
            await Promise.all(loadPromises);
        }

        // 加载系列卡片
        async function loadSeriesCards(categoryKey, seriesKey) {
            try {
                // 根据约定的文件命名规则查找文件
                const possibleFiles = await discoverMarkdownFiles(categoryKey);

                for (const fileName of possibleFiles) {
                    try {
                        const response = await fetch(`${categoryKey}/${fileName}`);
                        if (response.ok) {
                            const content = await response.text();
                            const card = parseMarkdownCard(content, fileName);

                            if (card) {
                                // 特殊处理chinese_history分类
                                if (categoryKey === 'chinese_history') {
                                    // 根据文件路径或内容判断属于哪个朝代系列
                                    let targetSeriesKey = null;

                                    if (fileName.includes('01-xia-dynasty') || card.metadata.keywords?.includes('夏朝')) {
                                        targetSeriesKey = 'xia_dynasty';
                                    } else if (fileName.includes('02-shang-dynasty') || card.metadata.keywords?.includes('商朝')) {
                                        targetSeriesKey = 'shang_dynasty';
                                    } else if (fileName.includes('03-western-zhou') || card.metadata.keywords?.includes('西周')) {
                                        targetSeriesKey = 'western_zhou';
                                    } else if (fileName.includes('04-spring-autumn') || card.metadata.keywords?.includes('春秋')) {
                                        targetSeriesKey = 'spring_autumn';
                                    } else if (fileName.includes('05-warring-states') || card.metadata.keywords?.includes('战国')) {
                                        targetSeriesKey = 'warring_states';
                                    } else if (fileName.includes('06-qin-dynasty') || card.metadata.keywords?.includes('秦朝')) {
                                        targetSeriesKey = 'qin_dynasty';
                                    } else if (fileName.includes('07-western-han') || card.metadata.keywords?.includes('西汉')) {
                                        targetSeriesKey = 'western_han';
                                    } else if (fileName.includes('08-xin-dynasty') || card.metadata.keywords?.includes('新朝')) {
                                        targetSeriesKey = 'xin_dynasty';
                                    } else if (fileName.includes('09-eastern-han') || card.metadata.keywords?.includes('东汉')) {
                                        targetSeriesKey = 'eastern_han';
                                    } else if (fileName.includes('10-three-kingdoms') || card.metadata.keywords?.includes('三国')) {
                                        targetSeriesKey = 'three_kingdoms';
                                    } else if (fileName.includes('09-jin-dynasty') || card.metadata.keywords?.includes('晋朝')) {
                                        targetSeriesKey = 'jin_dynasty';
                                    } else if (fileName.includes('10-southern-dynasties') || card.metadata.keywords?.includes('南朝')) {
                                        targetSeriesKey = 'southern_dynasties';
                                    } else if (fileName.includes('11-northern-dynasties') || card.metadata.keywords?.includes('北朝')) {
                                        targetSeriesKey = 'northern_dynasties';
                                    } else if (fileName.includes('10-southern-northern-dynasties') || card.metadata.keywords?.includes('南北朝')) {
                                        targetSeriesKey = 'southern_northern';
                                    } else if (fileName.includes('11-sui-dynasty') || card.metadata.keywords?.includes('隋朝')) {
                                        targetSeriesKey = 'sui_dynasty';
                                    } else if (fileName.includes('12-tang-dynasty') || card.metadata.keywords?.includes('唐朝')) {
                                        targetSeriesKey = 'tang_dynasty';
                                    } else if (fileName.includes('13-five-dynasties-ten-kingdoms') || card.metadata.keywords?.includes('五代十国')) {
                                        targetSeriesKey = 'five_dynasties_ten_kingdoms';
                                    } else if (fileName.includes('14-song-dynasty') || card.metadata.keywords?.includes('宋朝')) {
                                        targetSeriesKey = 'song_dynasty';
                                    } else if (fileName.includes('15-mongol-yuan') || card.metadata.keywords?.includes('蒙古') || card.metadata.keywords?.includes('元朝')) {
                                        targetSeriesKey = 'mongol_yuan';
                                    } else if (fileName.includes('16-ming-dynasty') || card.metadata.keywords?.includes('明朝')) {
                                        targetSeriesKey = 'ming_dynasty';
                                    } else if (fileName.includes('17-qing-dynasty') || card.metadata.keywords?.includes('清朝')) {
                                        targetSeriesKey = 'qing_dynasty';
                                    }

                                    if (targetSeriesKey === seriesKey) {
                                        const cardId = fileName.replace('.md', '').replace(/\//g, '-');
                                        knowledgeCards[cardId] = card;
                                        config.categories[categoryKey].series[seriesKey].cards.push(cardId);
                                    }
                                } else {
                                    // 其他分类使用原有逻辑
                                    if (card.metadata.series === config.categories[categoryKey].series[seriesKey].name) {
                                        const cardId = fileName.replace('.md', '').replace(/\//g, '-');
                                        knowledgeCards[cardId] = card;
                                        config.categories[categoryKey].series[seriesKey].cards.push(cardId);
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.warn(`加载文件 ${fileName} 失败:`, error);
                    }
                }
            } catch (error) {
                console.warn(`加载系列 ${seriesKey} 失败:`, error);
            }
        }

        // 发现Markdown文件（简化版本，实际应用中可能需要服务器端支持）
        async function discoverMarkdownFiles(categoryKey) {
            // 这里使用预定义的文件列表，实际应用中可能需要服务器端API
            // 您可以在这里添加新的markdown文件名
            const knownFiles = {
                'chinese_history': [
                    '01-xia-dynasty/overview.md',
                    '01-xia-dynasty/rulers.md',
                    '01-xia-dynasty/emperor-yu.md',
                    '01-xia-dynasty/emperor-qi.md',
                    '01-xia-dynasty/emperor-taikang.md',
                    '01-xia-dynasty/emperor-shaokang.md',
                    '01-xia-dynasty/emperor-jie.md',
                    '02-shang-dynasty/overview.md',
                    '02-shang-dynasty/rulers.md',
                    '02-shang-dynasty/emperor-tang.md',
                    '02-shang-dynasty/emperor-pangeng.md',
                    '02-shang-dynasty/emperor-wuding.md',
                    '02-shang-dynasty/emperor-zhou.md',
                    '03-western-zhou/overview.md',
                    '03-western-zhou/western-zhou-rulers.md',
                    '03-western-zhou/emperor-wuwang.md',
                    '03-western-zhou/emperor-chengwang.md',
                    '03-western-zhou/emperor-kangwang.md',
                    '03-western-zhou/emperor-zhaowang.md',
                    '03-western-zhou/emperor-muwang.md',
                    '03-western-zhou/emperor-liwang.md',
                    '03-western-zhou/emperor-xuanwang.md',
                    '03-western-zhou/emperor-youwang.md',
                    '04-spring-autumn/spring-autumn-period.md',
                    '04-spring-autumn/eastern-zhou-rulers.md',
                    '04-spring-autumn/spring-qi-huangong.md',
                    '04-spring-autumn/spring-jin-wengong.md',
                    '04-spring-autumn/spring-chu-zhuangwang.md',
                    '04-spring-autumn/spring-wu-helv.md',
                    '04-spring-autumn/spring-yue-goujian.md',
                    '05-warring-states/warring-states-period.md',
                    '05-warring-states/warring-states-rulers.md',
                    '05-warring-states/warring-qin-state.md',
                    '05-warring-states/warring-qi-state.md',
                    '05-warring-states/warring-chu-state.md',
                    '05-warring-states/warring-han-state.md',
                    '05-warring-states/warring-zhao-state.md',
                    '05-warring-states/warring-wei-state.md',
                    '05-warring-states/warring-yan-state.md',
                    '06-qin-dynasty/overview.md',
                    '06-qin-dynasty/rulers.md',
                    '06-qin-dynasty/emperor-shihuang.md',
                    '06-qin-dynasty/emperor-ershi.md',
                    '06-qin-dynasty/emperor-ziying.md',
                    '07-han-dynasty/overview.md',
                    '07-han-dynasty/rulers.md',
                    '07-western-han/overview.md',
                    '07-western-han/emperor-gaozu.md',
                    '07-western-han/emperor-huidi.md',
                    '07-western-han/empress-lu.md',
                    '07-western-han/emperor-wendi.md',
                    '07-western-han/emperor-jingdi.md',
                    '07-western-han/emperor-wudi.md',
                    '07-western-han/emperor-zhaodi.md',
                    '07-western-han/emperor-xuandi.md',
                    '07-western-han/late-emperors.md',
                    '08-xin-dynasty/wang-mang.md',
                    '09-eastern-han/overview.md',
                    '09-eastern-han/emperor-guangwu.md',
                    '09-eastern-han/emperor-mingdi.md',
                    '09-eastern-han/emperor-zhangdi.md',
                    '09-eastern-han/emperor-hedi.md',
                    '09-eastern-han/emperor-shangdi.md',
                    '09-eastern-han/emperor-andi.md',
                    '09-eastern-han/emperor-shundi.md',
                    '09-eastern-han/emperor-chongdi.md',
                    '09-eastern-han/emperor-zhidi.md',
                    '09-eastern-han/late-emperors.md',
                    '10-three-kingdoms/overview.md',
                    '10-three-kingdoms/cao-cao.md',
                    '10-three-kingdoms/liu-bei.md',
                    '10-three-kingdoms/sun-quan.md',
                    '10-three-kingdoms/zhuge-liang.md',
                    '10-three-kingdoms/guan-yu.md',
                    '10-three-kingdoms/zhang-fei.md',
                    '10-three-kingdoms/zhao-yun.md',
                    '10-three-kingdoms/zhou-yu.md',
                    '10-three-kingdoms/sima-yi.md',
                    '10-three-kingdoms/cao-pi.md',
                    '10-three-kingdoms/lu-xun.md',
                    '10-three-kingdoms/battle-of-guandu.md',
                    '10-three-kingdoms/battle-of-chibi.md',
                    '10-three-kingdoms/battle-of-yiling.md',
                    '10-three-kingdoms/battle-of-jieting.md',
                    '10-three-kingdoms/wei-kingdom.md',
                    '10-three-kingdoms/shu-kingdom.md',
                    '10-three-kingdoms/wu-kingdom.md',
                    '08-three-kingdoms/overview.md',
                    '09-jin-dynasty/overview.md',
                    '09-jin-dynasty/emperor-sima-yan-wudi.md',
                    '09-jin-dynasty/emperor-sima-zhong.md',
                    '09-jin-dynasty/emperor-sima-chi.md',
                    '09-jin-dynasty/emperor-sima-ye.md',
                    '09-jin-dynasty/emperor-sima-rui.md',
                    '09-jin-dynasty/emperor-sima-shao.md',
                    '09-jin-dynasty/emperor-sima-yan-chengdi.md',
                    '09-jin-dynasty/emperor-sima-yue.md',
                    '09-jin-dynasty/emperor-sima-dan.md',
                    '09-jin-dynasty/emperor-sima-pi.md',
                    '09-jin-dynasty/emperor-sima-yi.md',
                    '09-jin-dynasty/emperor-sima-yu.md',
                    '09-jin-dynasty/emperor-sima-yao.md',
                    '09-jin-dynasty/emperor-sima-dezong.md',
                    '09-jin-dynasty/emperor-sima-dewen.md',
                    '09-jin-dynasty/event-eight-princes-rebellion.md',
                    '09-jin-dynasty/event-yongjia-disaster.md',
                    '09-jin-dynasty/event-feishui-battle.md',
                    '09-jin-dynasty/event-wang-dun-rebellion.md',
                    '09-jin-dynasty/event-sujun-rebellion.md',
                    '09-jin-dynasty/event-huanwen-dictatorship.md',
                    '09-jin-dynasty/knowledge-network.md',
                    '10-southern-dynasties/southern-dynasties-overview.md',
                    '10-southern-dynasties/song-wudi-liuyu.md',
                    '10-southern-dynasties/song-shaodi-liuyifu.md',
                    '10-southern-dynasties/song-wendi-liuyilong.md',
                    '10-southern-dynasties/song-xiaowudi-liujun.md',
                    '10-southern-dynasties/song-two-deposed-emperors.md',
                    '10-southern-dynasties/song-mingdi-liuyu.md',
                    '10-southern-dynasties/song-shundi-liuzhun.md',
                    '10-southern-dynasties/qi-gaodi-xiaodaocheng.md',
                    '10-southern-dynasties/qi-wudi-xiaozi.md',
                    '10-southern-dynasties/qi-two-kings.md',
                    '10-southern-dynasties/qi-mingdi-xiaoluan.md',
                    '10-southern-dynasties/qi-donghunhou-xiaobaojuan.md',
                    '10-southern-dynasties/qi-hedi-xiaobaorong.md',
                    '10-southern-dynasties/liang-wudi-xiaoyan.md',
                    '10-southern-dynasties/liang-jianwendi-xiaogang.md',
                    '10-southern-dynasties/liang-yuandi-xiaoyi.md',
                    '10-southern-dynasties/liang-jingdi-xiaofangzhi.md',
                    '10-southern-dynasties/chen-wudi-chenbaxian.md',
                    '10-southern-dynasties/chen-wendi-chenqian.md',
                    '10-southern-dynasties/chen-feidi-chenbozong.md',
                    '10-southern-dynasties/chen-xuandi-chenxu.md',
                    '10-southern-dynasties/chen-houzhu-chenshubao.md',
                    '10-northern-dynasties/beiwei-daowudi-tuobagui.md',
                    '10-northern-dynasties/beiwei-mingyuandi-tuobasi.md',
                    '10-northern-dynasties/beiwei-taiwudi-tuobatao.md',
                    '10-northern-dynasties/beiwei-wenchengdi-tuobajun.md',
                    '10-northern-dynasties/beiwei-xianwendi-tuobahong.md',
                    '10-northern-dynasties/beiwei-xiaowendi-yuanhong.md',
                    '10-northern-dynasties/beiwei-xuanwudi-yuanke.md',
                    '10-northern-dynasties/beiwei-xiaomingdi-yuanxu.md',
                    '10-northern-dynasties/beiwei-xiaozhuangdi-yuanziyou.md',
                    '10-northern-dynasties/beiwei-houqi-zhudi.md',
                    '10-northern-dynasties/dongwei-xiaojingdi-yuanshanjian.md',
                    '10-northern-dynasties/xiwei-wendi-yuanbaoju.md',
                    '10-northern-dynasties/beiqi-wenxuandi-gaoyang.md',
                    '10-northern-dynasties/beiqi-houzhu-gaowei.md',
                    '10-northern-dynasties/beizhou-zhudi-yuwenshi.md',
                    '10-northern-dynasties/beizhou-wudi-yuwenyong.md',
                    '11-sui-dynasty/overview.md',
                    '11-sui-dynasty/emperor-wendi-yangjiang.md',
                    '11-sui-dynasty/emperor-yangdi-yangguang.md',
                    '11-sui-dynasty/sui-last-emperors.md',
                    '12-tang-dynasty/overview.md',
                    '12-tang-dynasty/emperor-gaozu-liyuan.md',
                    '12-tang-dynasty/emperor-taizong-lishimin.md',
                    '12-tang-dynasty/emperor-gaozong-lizhi.md',
                    '12-tang-dynasty/empress-wuzetian.md',
                    '12-tang-dynasty/tang-middle-emperors.md',
                    '12-tang-dynasty/emperor-xuanzong-lilongji.md',
                    '12-tang-dynasty/tang-anshi-emperors.md',
                    '12-tang-dynasty/tang-middle-decline-emperors.md',
                    '12-tang-dynasty/emperor-xianzong-lichun.md',
                    '12-tang-dynasty/tang-late-decline-emperors.md',
                    '12-tang-dynasty/tang-revival-emperors.md',
                    '12-tang-dynasty/tang-final-emperors.md',
                    '12-tang-dynasty/tang-last-emperors.md',
                    '13-five-dynasties-ten-kingdoms/later-liang.md',
                    '13-five-dynasties-ten-kingdoms/later-tang.md',
                    '13-five-dynasties-ten-kingdoms/later-jin.md',
                    '13-five-dynasties-ten-kingdoms/later-han.md',
                    '13-five-dynasties-ten-kingdoms/later-zhou.md',
                    '13-five-dynasties-ten-kingdoms/shu-kingdoms.md',
                    '13-five-dynasties-ten-kingdoms/wu-southern-tang.md',
                    '13-five-dynasties-ten-kingdoms/southern-han.md',
                    '13-five-dynasties-ten-kingdoms/smaller-kingdoms.md',
                    '13-five-dynasties-ten-kingdoms/northern-han.md',
                    '14-song-dynasty/overview.md',
                    '14-song-dynasty/northern-song-overview.md',
                    '14-song-dynasty/southern-song-overview.md',
                    '14-song-dynasty/emperor-taizu-zhaokuangyin.md',
                    '14-song-dynasty/emperor-taizong-zhaoguangyi.md',
                    '14-song-dynasty/emperor-zhenzong-zhaoheng.md',
                    '14-song-dynasty/emperor-renzong-zhaozhen.md',
                    '14-song-dynasty/emperor-yingzong-zhezong.md',
                    '14-song-dynasty/emperor-shenzong-zhaoxu.md',
                    '14-song-dynasty/emperor-huizong-zhaoqi.md',
                    '14-song-dynasty/emperor-qinzong-zhaohuan.md',
                    '14-song-dynasty/emperor-gaozong-zhaogou.md',
                    '14-song-dynasty/emperor-xiaozong-zhaoshen.md',
                    '14-song-dynasty/emperor-guangzong-ningzong.md',
                    '14-song-dynasty/emperor-lizong-zhaoyun.md',
                    '14-song-dynasty/emperor-duzong-zhaogi.md',
                    '14-song-dynasty/last-three-emperors.md',
                    '15-mongol-yuan/overview.md',
                    '15-mongol-yuan/emperor-genghis-khan.md',
                    '15-mongol-yuan/mongol-middle-emperors.md',
                    '15-mongol-yuan/emperor-shizu-kubilai.md',
                    '15-mongol-yuan/emperor-chengzong-temur.md',
                    '15-mongol-yuan/emperor-wuzong-haishan.md',
                    '15-mongol-yuan/emperor-renzong-ayurbarwada.md',
                    '15-mongol-yuan/emperor-yingzong-suddhipala.md',
                    '15-mongol-yuan/emperor-taiding-yesuntmr.md',
                    '15-mongol-yuan/yuan-middle-short-emperors.md',
                    '15-mongol-yuan/emperor-wenzong-tugtemur.md',
                    '15-mongol-yuan/emperor-shundi-toghontmr.md',
                    '16-ming-dynasty/emperor-zhuyuanzhang.md',
                    '16-ming-dynasty/emperor-zhu-yunwen.md',
                    '16-ming-dynasty/emperor-zhu-di.md',
                    '16-ming-dynasty/emperor-zhu-gaochi.md',
                    '16-ming-dynasty/emperor-zhu-zhanji.md',
                    '16-ming-dynasty/emperor-zhu-qizhen.md',
                    '16-ming-dynasty/emperor-zhu-qiyu.md',
                    '16-ming-dynasty/emperor-zhu-jianshen.md',
                    '16-ming-dynasty/emperor-zhu-youtang.md',
                    '16-ming-dynasty/emperor-zhu-houzhao.md',
                    '16-ming-dynasty/emperor-zhu-houzhong.md',
                    '16-ming-dynasty/emperor-zhu-zaiji.md',
                    '16-ming-dynasty/emperor-zhu-yijun.md',
                    '16-ming-dynasty/emperor-zhu-changluo.md',
                    '16-ming-dynasty/emperor-zhu-youxiao.md',
                    '16-ming-dynasty/emperor-zhu-youjian.md',
                    '17-qing-dynasty/emperor-nurhaci.md',
                    '17-qing-dynasty/emperor-huangtaiji.md',
                    '17-qing-dynasty/emperor-shunzhi.md',
                    '17-qing-dynasty/emperor-kangxi.md',
                    '17-qing-dynasty/emperor-yongzheng.md',
                    '17-qing-dynasty/emperor-qianlong.md',
                    '17-qing-dynasty/emperor-jiaqing.md',
                    '17-qing-dynasty/emperor-daoguang.md',
                    '17-qing-dynasty/emperor-xianfeng.md',
                    '17-qing-dynasty/emperor-tongzhi.md',
                    '17-qing-dynasty/emperor-guangxu.md',
                    '17-qing-dynasty/emperor-puyi.md'
                ],
                'history': ['chinese-ancient.md', 'european-ancient.md'],
                'math': ['algebra-basics.md'],
                'mechanical': [],
                'poetry': ['liuyuxi.md'],
                'astronomy': []
            };

            return knownFiles[categoryKey] || [];
        }

        // 解析Markdown卡片
        function parseMarkdownCard(content, fileName) {
            try {
                // 分离YAML Front Matter和Markdown内容
                const frontMatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/;
                const match = content.match(frontMatterRegex);
                
                if (!match) {
                    console.warn(`文件 ${fileName} 没有有效的YAML Front Matter`);
                    return null;
                }
                
                const yamlContent = match[1];
                const markdownContent = match[2];
                
                // 解析YAML
                const metadata = jsyaml.load(yamlContent);
                
                // 解析Markdown
                const htmlContent = marked.parse(markdownContent);
                
                return {
                    id: fileName.replace('.md', ''),
                    metadata: metadata,
                    content: htmlContent,
                    rawContent: markdownContent
                };
            } catch (error) {
                console.error(`解析文件 ${fileName} 失败:`, error);
                return null;
            }
        }

        // 渲染分类导航
        function renderCategoryNav() {
            const nav = document.getElementById('categoryNav');
            nav.innerHTML = '';
            
            for (const [categoryKey, category] of Object.entries(config.categories)) {
                const btn = document.createElement('button');
                btn.className = 'category-btn';
                btn.dataset.category = categoryKey;
                btn.innerHTML = `<i class="${category.icon}"></i> ${category.name}`;
                
                if (categoryKey === currentCategory) {
                    btn.classList.add('active');
                }
                
                nav.appendChild(btn);
            }
        }

        // 渲染知识库内容
        function renderKnowledgeSections() {
            const container = document.getElementById('knowledgeSections');
            container.innerHTML = '';
            
            for (const [categoryKey, category] of Object.entries(config.categories)) {
                const section = document.createElement('section');
                section.className = 'knowledge-section';
                section.dataset.category = categoryKey;
                
                if (categoryKey === currentCategory) {
                    section.classList.add('active');
                }
                
                section.innerHTML = `
                    <h2 class="section-title">
                        <i class="${category.icon}"></i> ${category.name}知识库
                    </h2>
                    <p class="section-subtitle">${category.description}</p>
                    <div class="cards-container">
                        ${renderSeriesCards(categoryKey, category)}
                    </div>
                `;
                
                container.appendChild(section);
            }
        }

        // 渲染系列卡片
        function renderSeriesCards(categoryKey, category) {
            let html = '';
            
            for (const [seriesKey, series] of Object.entries(category.series)) {
                if (series.cards.length === 0) continue;
                
                html += `
                    <div class="card-series">
                        <div class="series-header">
                            <h3 class="series-title">
                                <i class="${series.icon}"></i>
                                ${series.name}
                            </h3>
                            <span class="series-count">${series.cards.length}张卡片</span>
                        </div>
                        <div class="card-grid">
                            ${renderCards(series.cards)}
                        </div>
                    </div>
                `;
            }
            
            return html;
        }

        // 渲染卡片
        function renderCards(cardIds) {
            return cardIds.map(cardId => {
                const card = knowledgeCards[cardId];
                if (!card) return '';
                
                const keywords = card.metadata.keywords || [];
                const keywordTags = keywords.map(keyword => 
                    `<span class="keyword-tag">${keyword}</span>`
                ).join('');
                
                return `
                    <div class="knowledge-card" data-card="${cardId}">
                        <div class="card-title">${card.metadata.title}</div>
                        <div class="card-period">${card.metadata.period}</div>
                        <div class="card-keywords">${keywordTags}</div>
                        <div class="card-summary">${card.metadata.summary}</div>
                    </div>
                `;
            }).join('');
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 分类导航点击事件
            document.addEventListener('click', (e) => {
                if (e.target.closest('.category-btn')) {
                    const btn = e.target.closest('.category-btn');
                    switchCategory(btn.dataset.category);
                }
                
                // 卡片点击事件
                if (e.target.closest('.knowledge-card')) {
                    const card = e.target.closest('.knowledge-card');
                    openCard(card.dataset.card);
                }
                
                // 连接链接点击事件
                if (e.target.classList.contains('connection-link')) {
                    e.preventDefault();
                    const cardId = e.target.dataset.card;
                    if (cardId) {
                        openCard(cardId);
                    }
                }
            });

            // 搜索功能
            document.getElementById('searchBox').addEventListener('input', searchCards);

            // 模态框外部点击关闭
            document.getElementById('cardModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });

            // ESC键关闭模态框
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeModal();
                }
            });
        }

        // 切换分类
        function switchCategory(category) {
            currentCategory = category;
            
            // 更新导航按钮状态
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('active');

            // 切换知识库内容
            document.querySelectorAll('.knowledge-section').forEach(section => {
                section.classList.remove('active');
            });
            document.querySelector(`.knowledge-section[data-category="${category}"]`).classList.add('active');
        }

        // 打开卡片详情
        function openCard(cardId) {
            const card = knowledgeCards[cardId];
            if (!card) return;

            document.getElementById('modalTitle').textContent = card.metadata.title;
            
            // 处理连接
            let content = card.content;
            if (card.metadata.connections) {
                const connectionsHtml = `
                    <div class="connections">
                        <h4>知识联系</h4>
                        <div class="connection-links">
                            ${card.metadata.connections.map(conn => 
                                `<span class="connection-link" data-card="${conn.id}">
                                    ${conn.type}: ${conn.title}
                                </span>`
                            ).join('')}
                        </div>
                    </div>
                `;
                content += connectionsHtml;
            }
            
            document.getElementById('modalBody').innerHTML = content;
            // 显示模态框
            const modalEl = document.getElementById('cardModal');
            modalEl.style.display = 'block';
            // 打开后将滚动位置重置到顶部
            const modalContentEl = modalEl.querySelector('.modal-content');
            if (modalContentEl) {
                modalContentEl.scrollTop = 0;
            }
            const modalBodyEl = document.getElementById('modalBody');
            if (modalBodyEl) {
                modalBodyEl.scrollTop = 0;
            }
        }

        // 关闭模态框
        function closeModal() {
            const modalEl = document.getElementById('cardModal');
            // 先重置滚动位置，避免下次打开继承末尾
            const modalContentEl = modalEl.querySelector('.modal-content');
            if (modalContentEl) {
                modalContentEl.scrollTop = 0;
            }
            const modalBodyEl = document.getElementById('modalBody');
            if (modalBodyEl) {
                modalBodyEl.scrollTop = 0;
            }
            modalEl.style.display = 'none';
        }

        // 搜索卡片
        function searchCards() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const cards = document.querySelectorAll('.knowledge-card');
            
            cards.forEach(card => {
                const cardId = card.dataset.card;
                const cardData = knowledgeCards[cardId];
                
                if (!cardData) {
                    card.style.display = 'none';
                    return;
                }
                
                const searchableText = [
                    cardData.metadata.title,
                    cardData.metadata.summary,
                    ...(cardData.metadata.keywords || []),
                    cardData.rawContent
                ].join(' ').toLowerCase();
                
                if (searchableText.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = searchTerm ? 'none' : 'block';
                }
            });
        }

        // 隐藏加载提示
        function hideLoading() {
            document.getElementById('loadingMessage').style.display = 'none';
        }

        // 显示错误信息
        function showError() {
            document.getElementById('loadingMessage').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'block';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>
