我希望使用markdown创建自己的知识库，然后使用html静态渲染展示。知识库涵盖历史、数学、机械、天文等各个方面。一个markdown文件应该包含某一方面的内容，比如一个历史markdown文件，包含中国历史、欧洲历史、美国历史等等。帮我设计一个markdown模板用于创建知识库，并且适用于html展示。对于html展示，我希望可以采用卡片的形式（或者类似的形式）进行展示，让用户既能够专注了解知识，又能够将知识串联起来，例如，使用10张卡片展示中国历史，12张卡片展示欧洲历史等。

![1749960938225](image/outline/1749960938225.png)

对于html的展示如上图所示，每一个文件夹创建一个标签，如 'history'、'math'、'mechanical'，对应当前目录下的文件夹，然后每个文件夹下是一系列的markdown文件，用来表示相关的知识，例如 history下的ChineseSimple.md表示中国历史简述，使用标准markdown格式，包含题目、简述、标签、详细介绍等等（提供一个通用的模板）。在index初始页面展示简单信息，当点击每一个卡片后，弹出页面，用来显示详细的信息。如下所示

![1749966398176](image/outline/1749966398176.png)

index.html对项目目录下的文件夹进行遍历，然后对其中的所有markdown文件进行渲染。


参考如下代码

```
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人知识库 - History & More</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .category-nav {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 40px;
        }

        .category-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-weight: 500;
        }

        .category-btn:hover, .category-btn.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .knowledge-section {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .knowledge-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-title {
            color: white;
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .section-subtitle {
            color: rgba(255,255,255,0.9);
            text-align: center;
            margin-bottom: 40px;
            font-size: 1.1rem;
        }

        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }

        .card-series {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .card-series:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .series-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }

        .series-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .series-count {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 12px;
        }

        .knowledge-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .knowledge-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .knowledge-card:hover::before {
            transform: scaleX(1);
        }

        .knowledge-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
        }

        .card-title {
            font-size: 1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .card-period {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 8px;
            font-style: italic;
        }

        .card-keywords {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 10px;
        }

        .keyword-tag {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .card-summary {
            font-size: 0.85rem;
            color: #555;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            backdrop-filter: blur(5px);
            z-index: 1000;
            animation: modalFadeIn 0.3s ease;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 40px;
            border-radius: 20px;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from { transform: translate(-50%, -60%); opacity: 0; }
            to { transform: translate(-50%, -50%); opacity: 1; }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }

        .modal-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #f0f0f0;
            color: #333;
        }

        .modal-body {
            line-height: 1.6;
            color: #333;
        }

        .modal-body h3 {
            color: #667eea;
            margin: 20px 0 10px;
            font-size: 1.2rem;
        }

        .modal-body h4 {
            color: #2c3e50;
            margin: 15px 0 8px;
            font-size: 1.1rem;
        }

        .modal-body ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .modal-body li {
            margin-bottom: 5px;
        }

        .connections {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .connections h4 {
            color: #667eea !important;
            margin-bottom: 10px;
        }

        .connection-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .connection-link {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            text-decoration: none;
            font-size: 0.85rem;
            transition: all 0.3s ease;
        }

        .connection-link:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .search-container {
            margin-bottom: 30px;
            text-align: center;
        }

        .search-box {
            width: 100%;
            max-width: 500px;
            padding: 15px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            background: rgba(255,255,255,1);
        }

        @media (max-width: 768px) {
            .cards-container {
                grid-template-columns: 1fr;
            }
          
            .card-grid {
                grid-template-columns: 1fr;
            }
          
            .modal-content {
                margin: 20px;
                padding: 25px;
                max-width: calc(100% - 40px);
            }
          
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-book-open"></i> 个人知识库</h1>
            <p>探索历史脉络，串联知识体系</p>
        </header>

        <div class="search-container">
            <input type="text" class="search-box" placeholder="🔍 搜索知识卡片..." id="searchBox">
        </div>

        <nav class="category-nav">
            <button class="category-btn active" data-category="history">
                <i class="fas fa-landmark"></i> 历史
            </button>
            <button class="category-btn" data-category="math">
                <i class="fas fa-calculator"></i> 数学
            </button>
            <button class="category-btn" data-category="mechanical">
                <i class="fas fa-cog"></i> 机械
            </button>
            <button class="category-btn" data-category="astronomy">
                <i class="fas fa-space-shuttle"></i> 天文
            </button>
        </nav>

        <!-- 历史知识库 -->
        <section class="knowledge-section active" data-category="history">
            <h2 class="section-title"><i class="fas fa-scroll"></i> 历史知识库</h2>
            <p class="section-subtitle">通过时间线和主题分类，理解人类文明的演进过程</p>
          
            <div class="cards-container">
                <!-- 中国历史系列 -->
                <div class="card-series">
                    <div class="series-header">
                        <h3 class="series-title">
                            <i class="fas fa-dragon"></i>
                            中国历史
                        </h3>
                        <span class="series-count">10张卡片</span>
                    </div>
                    <div class="card-grid">
                        <div class="knowledge-card" data-card="china-1">
                            <div class="card-title">先秦时期</div>
                            <div class="card-period">公元前21世纪 - 前221年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">夏商周</span>
                                <span class="keyword-tag">百家争鸣</span>
                            </div>
                            <div class="card-summary">中国历史的起源，从夏朝建立到春秋战国的思想繁荣时期...</div>
                        </div>
                        <div class="knowledge-card" data-card="china-2">
                            <div class="card-title">秦汉统一</div>
                            <div class="card-period">公元前221年 - 公元220年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">统一</span>
                                <span class="keyword-tag">丝绸之路</span>
                            </div>
                            <div class="card-summary">秦始皇统一中国，汉朝建立中央集权制度，开辟丝绸之路...</div>
                        </div>
                        <div class="knowledge-card" data-card="china-3">
                            <div class="card-title">魏晋南北朝</div>
                            <div class="card-period">公元220年 - 589年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">分裂</span>
                                <span class="keyword-tag">民族融合</span>
                            </div>
                            <div class="card-summary">三国鼎立，南北朝对峙，民族大融合的重要历史时期...</div>
                        </div>
                        <div class="knowledge-card" data-card="china-4">
                            <div class="card-title">隋唐盛世</div>
                            <div class="card-period">公元581年 - 907年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">盛世</span>
                                <span class="keyword-tag">开放</span>
                            </div>
                            <div class="card-summary">中华文明的黄金时代，国力强盛，文化繁荣...</div>
                        </div>
                        <div class="knowledge-card" data-card="china-5">
                            <div class="card-title">宋元变革</div>
                            <div class="card-period">公元960年 - 1368年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">科技</span>
                                <span class="keyword-tag">商业</span>
                            </div>
                            <div class="card-summary">科技革命与商业发展的重要时期...</div>
                        </div>
                        <div class="knowledge-card" data-card="china-6">
                            <div class="card-title">明清专制</div>
                            <div class="card-period">公元1368年 - 1912年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">专制</span>
                                <span class="keyword-tag">闭关</span>
                            </div>
                            <div class="card-summary">封建社会的最后阶段，专制制度达到顶峰...</div>
                        </div>
                    </div>
                </div>

                <!-- 欧洲历史系列 -->
                <div class="card-series">
                    <div class="series-header">
                        <h3 class="series-title">
                            <i class="fas fa-crown"></i>
                            欧洲历史
                        </h3>
                        <span class="series-count">12张卡片</span>
                    </div>
                    <div class="card-grid">
                        <div class="knowledge-card" data-card="europe-1">
                            <div class="card-title">古希腊文明</div>
                            <div class="card-period">公元前8世纪 - 前146年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">民主</span>
                                <span class="keyword-tag">哲学</span>
                            </div>
                            <div class="card-summary">城邦制度、民主政治、哲学思辨的发源地...</div>
                        </div>
                        <div class="knowledge-card" data-card="europe-2">
                            <div class="card-title">古罗马帝国</div>
                            <div class="card-period">公元前753年 - 公元476年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">帝国</span>
                                <span class="keyword-tag">法律</span>
                            </div>
                            <div class="card-summary">从共和到帝制，罗马法的建立与传播...</div>
                        </div>
                        <div class="knowledge-card" data-card="europe-3">
                            <div class="card-title">中世纪</div>
                            <div class="card-period">公元476年 - 1453年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">封建</span>
                                <span class="keyword-tag">基督教</span>
                            </div>
                            <div class="card-summary">封建制度与基督教文明的兴起...</div>
                        </div>
                        <div class="knowledge-card" data-card="europe-4">
                            <div class="card-title">文艺复兴</div>
                            <div class="card-period">14-16世纪</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">人文</span>
                                <span class="keyword-tag">艺术</span>
                            </div>
                            <div class="card-summary">人文主义的兴起，艺术与科学的繁荣...</div>
                        </div>
                        <div class="knowledge-card" data-card="europe-5">
                            <div class="card-title">大航海时代</div>
                            <div class="card-period">15-17世纪</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">探险</span>
                                <span class="keyword-tag">殖民</span>
                            </div>
                            <div class="card-summary">地理大发现与殖民扩张...</div>
                        </div>
                        <div class="knowledge-card" data-card="europe-6">
                            <div class="card-title">启蒙运动</div>
                            <div class="card-period">17-18世纪</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">理性</span>
                                <span class="keyword-tag">科学</span>
                            </div>
                            <div class="card-summary">理性主义与科学革命的时代...</div>
                        </div>
                    </div>
                </div>

                <!-- 美国历史系列 -->
                <div class="card-series">
                    <div class="series-header">
                        <h3 class="series-title">
                            <i class="fas fa-flag-usa"></i>
                            美国历史
                        </h3>
                        <span class="series-count">8张卡片</span>
                    </div>
                    <div class="card-grid">
                        <div class="knowledge-card" data-card="usa-1">
                            <div class="card-title">殖民地时期</div>
                            <div class="card-period">1607年 - 1776年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">殖民</span>
                                <span class="keyword-tag">移民</span>
                            </div>
                            <div class="card-summary">欧洲移民的大规模迁移与殖民地的建立...</div>
                        </div>
                        <div class="knowledge-card" data-card="usa-2">
                            <div class="card-title">独立战争</div>
                            <div class="card-period">1775年 - 1783年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">独立</span>
                                <span class="keyword-tag">革命</span>
                            </div>
                            <div class="card-summary">反对英国统治，争取独立的革命战争...</div>
                        </div>
                        <div class="knowledge-card" data-card="usa-3">
                            <div class="card-title">西进运动</div>
                            <div class="card-period">19世纪</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">扩张</span>
                                <span class="keyword-tag">淘金</span>
                            </div>
                            <div class="card-summary">向西部扩张，开发边疆的历史进程...</div>
                        </div>
                        <div class="knowledge-card" data-card="usa-4">
                            <div class="card-title">南北战争</div>
                            <div class="card-period">1861年 - 1865年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">内战</span>
                                <span class="keyword-tag">解放</span>
                            </div>
                            <div class="card-summary">为统一和解放奴隶而进行的内战...</div>
                        </div>
                    </div>
                </div>

                <!-- 古代文明系列 -->
                <div class="card-series">
                    <div class="series-header">
                        <h3 class="series-title">
                            <i class="fas fa-monument"></i>
                            古代文明
                        </h3>
                        <span class="series-count">6张卡片</span>
                    </div>
                    <div class="card-grid">
                        <div class="knowledge-card" data-card="ancient-1">
                            <div class="card-title">美索不达米亚</div>
                            <div class="card-period">前3500年 - 前539年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">楔形文字</span>
                                <span class="keyword-tag">法典</span>
                            </div>
                            <div class="card-summary">人类最早的城市文明，楔形文字的发明...</div>
                        </div>
                        <div class="knowledge-card" data-card="ancient-2">
                            <div class="card-title">古代埃及</div>
                            <div class="card-period">前3100年 - 前30年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">金字塔</span>
                                <span class="keyword-tag">法老</span>
                            </div>
                            <div class="card-summary">尼罗河文明，金字塔与法老的神秘王国...</div>
                        </div>
                        <div class="knowledge-card" data-card="ancient-3">
                            <div class="card-title">印度河文明</div>
                            <div class="card-period">前2600年 - 前1900年</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">城市规划</span>
                                <span class="keyword-tag">贸易</span>
                            </div>
                            <div class="card-summary">高度发达的城市文明与贸易网络...</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 数学知识库 -->
        <section class="knowledge-section" data-category="math">
            <h2 class="section-title"><i class="fas fa-calculator"></i> 数学知识库</h2>
            <p class="section-subtitle">探索数学的逻辑美与实用性</p>
          
            <div class="cards-container">
                <div class="card-series">
                    <div class="series-header">
                        <h3 class="series-title">
                            <i class="fas fa-square-root-alt"></i>
                            基础数学
                        </h3>
                        <span class="series-count">8张卡片</span>
                    </div>
                    <div class="card-grid">
                        <div class="knowledge-card" data-card="math-1">
                            <div class="card-title">代数基础</div>
                            <div class="card-period">基础概念</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">方程</span>
                                <span class="keyword-tag">函数</span>
                            </div>
                            <div class="card-summary">代数运算、方程求解、函数概念...</div>
                        </div>
                        <div class="knowledge-card" data-card="math-2">
                            <div class="card-title">几何学</div>
                            <div class="card-period">空间概念</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">图形</span>
                                <span class="keyword-tag">证明</span>
                            </div>
                            <div class="card-summary">平面几何、立体几何、几何证明...</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 机械知识库 -->
        <section class="knowledge-section" data-category="mechanical">
            <h2 class="section-title"><i class="fas fa-cog"></i> 机械知识库</h2>
            <p class="section-subtitle">理解机械原理与工程应用</p>
          
            <div class="cards-container">
                <div class="card-series">
                    <div class="series-header">
                        <h3 class="series-title">
                            <i class="fas fa-tools"></i>
                            机械原理
                        </h3>
                        <span class="series-count">6张卡片</span>
                    </div>
                    <div class="card-grid">
                        <div class="knowledge-card" data-card="mech-1">
                            <div class="card-title">简单机械</div>
                            <div class="card-period">基础原理</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">杠杆</span>
                                <span class="keyword-tag">滑轮</span>
                            </div>
                            <div class="card-summary">杠杆、滑轮、斜面等基本机械原理...</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 天文知识库 -->
        <section class="knowledge-section" data-category="astronomy">
            <h2 class="section-title"><i class="fas fa-space-shuttle"></i> 天文知识库</h2>
            <p class="section-subtitle">探索宇宙的奥秘与规律</p>
          
            <div class="cards-container">
                <div class="card-series">
                    <div class="series-header">
                        <h3 class="series-title">
                            <i class="fas fa-globe"></i>
                            太阳系
                        </h3>
                        <span class="series-count">9张卡片</span>
                    </div>
                    <div class="card-grid">
                        <div class="knowledge-card" data-card="astro-1">
                            <div class="card-title">太阳</div>
                            <div class="card-period">恒星</div>
                            <div class="card-keywords">
                                <span class="keyword-tag">核聚变</span>
                                <span class="keyword-tag">光热</span>
                            </div>
                            <div class="card-summary">太阳的结构、核聚变反应与能量释放...</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 模态框 -->
    <div class="modal" id="cardModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">卡片标题</h2>
                <button class="close-btn" onclick="closeModal()">×</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 详细内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <script>
        // 卡片数据
        const cardData = {
            'china-1': {
                title: '先秦时期',
                period: '公元前21世纪 - 公元前221年',
                keywords: ['夏商周', '春秋战国', '百家争鸣'],
                content: `
                    <h3>核心内容</h3>
                    <h4>夏朝建立 (约公元前2070年)</h4>
                    <p>中国历史上第一个世袭制王朝，标志着中国进入文明社会。</p>
                  
                    <h4>商朝文明 (约公元前1600-1046年)</h4>
                    <p>甲骨文的发明和使用，青铜器文化达到鼎盛期，形成了成熟的政治制度。</p>
                  
                    <h4>周朝分封 (公元前1046-256年)</h4>
                    <p>建立分封制度，推行礼乐文明，奠定了中国传统文化的基础。</p>
                  
                    <h4>春秋战国 (公元前770-221年)</h4>
                    <p>诸侯争霸，百家争鸣，思想文化空前繁荣，为后世奠定了思想基础。</p>
                  
                    <h3>重要人物</h3>
                    <ul>
                        <li><strong>孔子</strong> (公元前551-479年): 儒家思想创始人，提出"仁"的核心理念</li>
                        <li><strong>老子</strong>: 道家思想创始人，著有《道德经》</li>
                        <li><strong>秦始皇</strong> (公元前259-210年): 统一六国的第一位皇帝</li>
                    </ul>
                  
                    <h3>文化成就</h3>
                    <ul>
                        <li><strong>甲骨文</strong>: 中国最早的成熟文字系统</li>
                        <li><strong>青铜器</strong>: 商周时期的重要文化标志</li>
                        <li><strong>诸子百家</strong>: 儒、道、法、墨等思想流派的形成</li>
                    </ul>
                  
                    <div class="connections">
                        <h4>知识联系</h4>
                        <div class="connection-links">
                            <a href="#" class="connection-link" onclick="openCard('china-2')">→ 秦汉统一</a>
                            <a href="#" class="connection-link" onclick="openCard('ancient-1')">对比: 美索不达米亚文明</a>
                        </div>
                    </div>
                `
            },
            'china-2': {
                title: '秦汉统一',
                period: '公元前221年 - 公元220年',
                keywords: ['统一', '中央集权', '丝绸之路'],
                content: `
                    <h3>核心内容</h3>
                    <h4>秦朝统一 (公元前221-206年)</h4>
                    <p>秦始皇统一六国，建立中央集权制度，实行"书同文、车同轨、统一度量衡"。</p>
                  
                    <h4>汉朝建立 (公元前206年-公元220年)</h4>
                    <p>分为西汉和东汉两个阶段，进一步完善中央集权制度。</p>
                  
                    <h4>制度创新</h4>
                    <p>建立郡县制、察举制等政治制度，为后世封建社会奠定制度基础。</p>
                  
                    <h4>对外交流</h4>
                    <p>张骞出使西域，开辟丝绸之路，促进中西文化交流。</p>
                  
                    <h3>重要人物</h3>
                    <ul>
                        <li><strong>秦始皇</strong>: 统一中国，建立中央集权制度</li>
                        <li><strong>汉武帝</strong> (公元前156-87年): 开疆拓土，独尊儒术</li>
                        <li><strong>张骞</strong>: 开辟丝绸之路的外交使者</li>
                    </ul>
                  
                    <div class="connections">
                        <h4>知识联系</h4>
                        <div class="connection-links">
                            <a href="#" class="connection-link" onclick="openCard('china-1')">← 先秦时期</a>
                            <a href="#" class="connection-link" onclick="openCard('china-3')">→ 魏晋南北朝</a>
                            <a href="#" class="connection-link" onclick="openCard('europe-2')">对比: 古罗马帝国</a>
                        </div>
                    </div>
                `
            },
            'europe-1': {
                title: '古希腊文明',
                period: '公元前8世纪 - 公元前146年',
                keywords: ['城邦制', '民主制', '哲学', '艺术'],
                content: `
                    <h3>核心内容</h3>
                    <h4>城邦制度</h4>
                    <p>雅典、斯巴达等独立城邦形成独特的政治制度和文化特色。</p>
                  
                    <h4>民主发展</h4>
                    <p>雅典创立直接民主制，公民参与政治决策，为后世民主制度奠定基础。</p>
                  
                    <h4>哲学思想</h4>
                    <p>苏格拉底、柏拉图、亚里士多德建立了西方哲学思辨传统。</p>
                  
                    <h4>文化成就</h4>
                    <p>史诗、戏剧、建筑、雕塑达到艺术高峰，影响后世文化发展。</p>
                  
                    <h3>重要人物</h3>
                    <ul>
                        <li><strong>伯里克利</strong> (公元前495-429年): 雅典民主制的完善者</li>
                        <li><strong>苏格拉底</strong> (公元前469-399年): 古希腊哲学家，理性思辨的倡导者</li>
                        <li><strong>亚历山大大帝</strong> (公元前356-323年): 建立横跨欧亚非的大帝国</li>
                    </ul>
                  
                    <div class="connections">
                        <h4>知识联系</h4>
                        <div class="connection-links">
                            <a href="#" class="connection-link" onclick="openCard('europe-2')">→ 古罗马帝国</a>
                            <a href="#" class="connection-link" onclick="openCard('china-1')">对比: 中国先秦时期</a>
                            <a href="#" class="connection-link" onclick="openCard('europe-4')">影响: 文艺复兴</a>
                        </div>
                    </div>
                `
            }
        };

        // 页面功能
        function switchCategory(category) {
            // 切换导航按钮状态
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('active');

            // 切换知识库内容
            document.querySelectorAll('.knowledge-section').forEach(section => {
                section.classList.remove('active');
            });
            document.querySelector(`.knowledge-section[data-category="${category}"]`).classList.add('active');
        }

        function openCard(cardId) {
            const card = cardData[cardId];
            if (!card) return;

            document.getElementById('modalTitle').textContent = card.title;
            document.getElementById('modalBody').innerHTML = card.content;
            document.getElementById('cardModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('cardModal').style.display = 'none';
        }

        function searchCards() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const cards = document.querySelectorAll('.knowledge-card');
          
            cards.forEach(card => {
                const title = card.querySelector('.card-title').textContent.toLowerCase();
                const keywords = Array.from(card.querySelectorAll('.keyword-tag'))
                    .map(tag => tag.textContent.toLowerCase()).join(' ');
                const summary = card.querySelector('.card-summary').textContent.toLowerCase();
              
                if (title.includes(searchTerm) || keywords.includes(searchTerm) || summary.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = searchTerm ? 'none' : 'block';
                }
            });
        }

        // 事件监听
        document.addEventListener('DOMContentLoaded', function() {
            // 分类导航点击事件
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    switchCategory(btn.dataset.category);
                });
            });

            // 卡片点击事件
            document.querySelectorAll('.knowledge-card').forEach(card => {
                card.addEventListener('click', () => {
                    openCard(card.dataset.card);
                });
            });

            // 搜索功能
            document.getElementById('searchBox').addEventListener('input', searchCards);

            // 模态框外部点击关闭
            document.getElementById('cardModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });

            // ESC键关闭模态框
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeModal();
                }
            });
        });
    </script>
</body>
</html>
```
