{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-10T08:28:17.339Z", "updatedAt": "2025-08-10T08:28:17.371Z", "resourceCount": 7}, "resources": [{"id": "chinese-history-expert", "source": "project", "protocol": "role", "name": "Chinese History Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/chinese-history-expert/chinese-history-expert.role.md", "metadata": {"createdAt": "2025-08-10T08:28:17.350Z", "updatedAt": "2025-08-10T08:28:17.350Z", "scannedAt": "2025-08-10T08:28:17.350Z", "path": "role/chinese-history-expert/chinese-history-expert.role.md"}}, {"id": "content-expansion-pm", "source": "project", "protocol": "role", "name": "Content Expansion Pm 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/content-expansion-pm/content-expansion-pm.role.md", "metadata": {"createdAt": "2025-08-10T08:28:17.354Z", "updatedAt": "2025-08-10T08:28:17.354Z", "scannedAt": "2025-08-10T08:28:17.354Z", "path": "role/content-expansion-pm/content-expansion-pm.role.md"}}, {"id": "content-planning-expert", "source": "project", "protocol": "role", "name": "Content Planning Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/content-planning-expert/content-planning-expert.role.md", "metadata": {"createdAt": "2025-08-10T08:28:17.359Z", "updatedAt": "2025-08-10T08:28:17.359Z", "scannedAt": "2025-08-10T08:28:17.359Z", "path": "role/content-planning-expert/content-planning-expert.role.md"}}, {"id": "knowledge-content-developer", "source": "project", "protocol": "role", "name": "Knowledge Content Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/knowledge-content-developer/knowledge-content-developer.role.md", "metadata": {"createdAt": "2025-08-10T08:28:17.360Z", "updatedAt": "2025-08-10T08:28:17.360Z", "scannedAt": "2025-08-10T08:28:17.360Z", "path": "role/knowledge-content-developer/knowledge-content-developer.role.md"}}, {"id": "knowledge-context-manager", "source": "project", "protocol": "role", "name": "Knowledge Context Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/knowledge-context-manager/knowledge-context-manager.role.md", "metadata": {"createdAt": "2025-08-10T08:28:17.364Z", "updatedAt": "2025-08-10T08:28:17.364Z", "scannedAt": "2025-08-10T08:28:17.364Z", "path": "role/knowledge-context-manager/knowledge-context-manager.role.md"}}, {"id": "learning-experience-analyst", "source": "project", "protocol": "role", "name": "Learning Experience Analyst 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/learning-experience-analyst/learning-experience-analyst.role.md", "metadata": {"createdAt": "2025-08-10T08:28:17.367Z", "updatedAt": "2025-08-10T08:28:17.367Z", "scannedAt": "2025-08-10T08:28:17.367Z", "path": "role/learning-experience-analyst/learning-experience-analyst.role.md"}}, {"id": "miniprogram-architect", "source": "project", "protocol": "role", "name": "Miniprogram Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/miniprogram-architect/miniprogram-architect.role.md", "metadata": {"createdAt": "2025-08-10T08:28:17.370Z", "updatedAt": "2025-08-10T08:28:17.370Z", "scannedAt": "2025-08-10T08:28:17.370Z", "path": "role/miniprogram-architect/miniprogram-architect.role.md"}}], "stats": {"totalResources": 7, "byProtocol": {"role": 7}, "bySource": {"project": 7}}}